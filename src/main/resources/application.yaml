server:
  port:
    8080
  use-forward-headers: false

spring:
  application:
    name: api-gateway

  webflux:
    base-path: /api

  cloud:
    gateway:
      globalcors:
        add-to-simple-url-handler-mapping: true
        cors-configurations:
          '[/**]':
            allowedOrigins:
            - http://route-transport.duckdns.org
            - http://route-transport.duckdns.local.org
            - http://auth.route-transport.duckdns.org
            - http://auth.route-transport.duckdns.local.org
            - http://auth.civil-aviation.pro
            - http://civil-aviation.pro
            - localhost:8082
            - localhost:8083
            allowedMethods:
              - GET
              - POST
              - OPTIONS
            allowedHeaders:
              - Authorization
              - Content-Type
              - X-XSRF-TOKEN
              - X-Tenant-Id
            allowCredentials: true
      routes:
        - id: core-service-route
          uri: http://core-backend:8083
          predicates:
            - Path=/api/core-service/**
          filters:
            - RewritePath=/api/core-service/(?<remaining>.*), /api/${remaining}
            - RemoveRequestHeader=Cookie
            - TokenRelay=
        - id: user-service-route
          uri: http://auth-server:8082
          predicates:
            - Path=/api/user-service/**
          filters:
            - RewritePath=/api/user-service/(?<remaining>.*), /api/${remaining}
            - RemoveRequestHeader=Cookie
            - AddRequestHeader=Authorization, "#{request.headers.Authorization}"
            - TokenRelay=
        - id: auth-route
          uri: http://auth-server:8082
          predicates:
            - Path=/**
          filters:
            - name: RequestHeaderToRequestUri
            - AddRequestHeader=Authorization, "#{request.headers.Authorization}"

  security:
    logout-url: /logout
    oauth2:
      client:
        registration:
          cap-api-gateway:
            provider: cap-api-gateway
            client-id: ${OAUTH_CLIENT_ID:cap-api-gateway}
            client-secret: ${OAUTH_CLIENT_SECRET:cap-api-gateway-secret}
            authorization-grant-type: authorization_code
            redirect-uri: "http://route-transport.duckdns.org/api/login/oauth2/code/{registrationId}"
            post-logout-redirect-url: "{baseUrl}/"
            scope:
              - openid
        provider:
          cap-api-gateway:
            authorization-uri: http://auth.route-transport.duckdns.org/oauth2/authorize
            token-uri: http://auth-server:8082/oauth2/token
            user-info-uri: http://auth-server:8082/userinfo
            jwk-set-uri: http://auth-server:8082/oauth2/jwks
            user-name-attribute: sub

redis-config:
  host: redis
  port: 6379
#  username: ${REDIS_USERNAME:}
#  password: ${REDIS_PASSWORD:}
  ssl-enabled: ${REDIS_SSL_ENABLES:false}
  keystore-location: ${REDIS_KEYSTORE_LOCATION:}
  truststore-location: ${REDIS_TRUSTSTORE_LOCATION:}

web-session:
  timeout-in-min: ${WEB_SESSION_TIMEOUT_IN_MIN:15}

tenant-config:
  tenants:
    - tenant-id: 6db16b2d-c3ce-469c-8ddb-7705c6c8981f
      successful-login-url: http://route-transport.duckdns.org/

logging:
  level:
    org.springframework.cloud.gateway: TRACE
    reactor.netty.http.server.HttpServer: DEBUG
    org.springframework.web: DEBUG
    org.springframework.security: DEBUG

